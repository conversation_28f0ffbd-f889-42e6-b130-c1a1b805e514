# 啮齿动物致癌性预测模型 (Rodent Carcinogenicity Prediction Model)

基于XGBoost的啮齿动物致癌性预测系统，使用Avalon分子指纹，集成应用域评估功能，提供RESTful API接口。

## 项目概述

本项目专门用于预测化合物对啮齿动物的致癌性，包括：
- **XGBoost机器学习模型**：基于啮齿动物致癌性数据训练的分类模型
- **Avalon分子指纹**：2048维分子特征表示
- **应用域评估**：评估预测结果的可靠性
- **FastAPI接口**：提供标准化的RESTful API服务
- **批量预测**：支持单个和批量SMILES预测

## 项目结构

```
carcinogenicity-model/
├── app/                          # 主应用目录
│   ├── main.py                   # FastAPI应用入口
│   ├── Predict_with_AD.py        # 核心预测脚本（集成应用域检查）
│   ├── AD/                       # 应用域分析模块
│   │   ├── ADSAL_C.py           # 应用域分析主程序
│   │   ├── AD_figure_classifier.py # 可视化工具
│   │   └── NSGCorrelation_New.py # NSG相关性分析
│   ├── XGBoost/                  # XGBoost模型训练
│   │   └── Rodent_XGBoost.py    # 啮齿动物致癌性模型训练脚本
│   └── save_model/               # 保存的模型文件
│       ├── Rodent_clean_Avalon_XGB_best_model.joblib  # 训练好的XGBoost模型
│       ├── Rodent_clean_Avalon_XGB_model_info.pkl     # 模型信息和预处理器
│       └── Rodent_clean_Avalon_XGB_predictions.xlsx   # 训练集预测结果
├── commands.txt                 # 依赖包安装命令
└── README.md                    # 项目说明文档
```

## 快速开始

### 环境准备

按照commands.txt中的命令安装依赖包：

```bash
# 创建Python 3.10环境
conda create -n rodent-carcinogenicity python=3.10
conda activate rodent-carcinogenicity

# 安装核心机器学习包
pip install xgboost==3.0.2
pip install pandas==2.3.0
pip install numpy==1.24.3
pip install scikit-learn==1.6.1
pip install scipy==1.15.3
pip install joblib==1.5.1
pip install seaborn==0.13.2

# 安装化学信息学包（必须通过conda安装）
conda install -c conda-forge rdkit=2025.3.3

# 安装Web框架
pip install pydantic==1.10.10 typing_extensions==4.7.1 fastapi==0.95.2 uvicorn==0.22.0

# 安装文件处理包
pip install openpyxl
```

### 启动服务

#### 方法1: 使用启动脚本

```bash
# 进入项目目录
cd carcinogenicity-model

# 启动API服务
python start_server.py
```

#### 方法2: 直接使用uvicorn

```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

服务启动后，访问：
- API文档：http://localhost:8000/docs
- 健康检查：http://localhost:8000/health

#### 使用独立预测脚本

```bash
# 预测单个SMILES
python app/Predict_with_AD.py --smiles "CCO"

# 批量预测CSV文件
python app/Predict_with_AD.py --input_csv input.csv --smiles_column SMILES

# 直接运行（预测内置测试化合物）
python app/Predict_with_AD.py
```

## 使用说明

### API接口

#### 1. 批量预测接口

**POST** `/predict`

请求体：
```json
{
  "smiles_list": ["CCO", "c1ccccc1", "CC(=O)O"]
}
```

响应示例：
```json
[
  {
    "SMILES": "CCO",
    "Rodent_clean_Avalon_XGB": {
      "predicted_class": 0,
      "probabilities": {
        "class_0": 0.8234,
        "class_1": 0.1766
      },
      "applicability_domain": {
        "status": "In-Domain"
      }
    }
  }
]
```

#### 2. 健康检查接口

**GET** `/health`

响应：
```json
{
  "status": "ok",
  "message": "Service is running normally"
}
```

### 预测结果说明

- **predicted_class**: 0表示非致癌，1表示致癌
- **probabilities**: class_0为非致癌概率，class_1为致癌概率
- **applicability_domain**: 应用域状态，"In-Domain"表示在模型适用范围内

## 模型信息

### 模型详情

- **模型名称**: Rodent_clean_Avalon_XGB
- **模型类型**: XGBoost分类器
- **训练数据**: 啮齿动物致癌性数据
- **特征类型**: Avalon分子指纹（2048维）
- **预测目标**: 二分类（致癌/非致癌）

### 应用域评估

模型集成了简化的应用域检查功能，用于评估预测结果的可靠性：
- **In-Domain**: 化合物在模型适用范围内，预测结果可靠
- **Out-of-Domain**: 化合物超出模型适用范围，预测结果需谨慎使用

## 开发指南

### 模型文件结构

模型文件位于 `app/save_model/` 目录：
- `Rodent_clean_Avalon_XGB_best_model.joblib`: 训练好的XGBoost模型
- `Rodent_clean_Avalon_XGB_model_info.pkl`: 模型信息和预处理器
- `Rodent_clean_Avalon_XGB_predictions.xlsx`: 训练集预测结果

### 模型训练

模型训练脚本位于 `app/XGBoost/Rodent_XGBoost.py`，包含完整的数据预处理、特征提取、模型训练和评估流程。

## 注意事项

1. **RDKit安装**: 必须通过conda安装RDKit，pip版本可能存在兼容性问题
2. **模型文件**: 确保模型文件已正确放置在 `app/save_model/` 目录
3. **内存使用**: 大批量预测时注意内存使用情况
4. **应用域**: 对于应用域外的化合物，预测结果可靠性较低

## 技术栈

- **机器学习**: XGBoost
- **化学信息学**: RDKit
- **Web框架**: FastAPI
- **数据处理**: Pandas, NumPy
- **特征提取**: Avalon分子指纹

---

**版本**: 1.0.0
**专用于**: 啮齿动物致癌性预测